<?php

namespace App\Http\Middleware;

use App\Services\ContentSecurityPolicy\NonceGenerator;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class EnhancedContentSecurityPolicy
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Process the request
        $response = $next($request);

        // Skip CSP entirely in local development environment
        if (app()->environment('local', 'development')) {
            return $response;
        }

        // Skip CSP for redirects
        if ($response->isRedirect()) {
            return $response;
        }

        // Skip CSP for certain file types and admin routes during login
        if ($this->shouldSkipCSP($request)) {
            return $response;
        }
        
        // Skip CSP for admin login/redirect flows and dashboard to prevent interference
        $skipPaths = ['admin', 'sign-in', 'signin', 'login', 'logout', 'dashboard', '/', 'home'];
        foreach ($skipPaths as $path) {
            if ($request->path() === $path || $request->path() === trim($path, '/') || str_starts_with($request->path(), $path . '/')) {
                return $response;
            }
        }

        // Check if CSP is enabled
        if (!config('csp.enabled', true)) {
            return $response;
        }

        // Generate nonce for this request
        $nonce = NonceGenerator::getNonce();

        // Get CSP configuration based on environment
        $environment = app()->environment('production') ? 'production' : 'development';
        $cspConfig = config("csp.{$environment}", []);

        // Build CSP header
        $cspHeader = $this->buildCspHeader($cspConfig, $nonce);

        // Remove any existing CSP headers that might conflict
        $response->headers->remove('Content-Security-Policy');
        $response->headers->remove('Content-Security-Policy-Report-Only');
        $response->headers->remove('X-Content-Security-Policy');
        $response->headers->remove('X-WebKit-CSP');

        // Set the appropriate CSP header
        $headerName = config('csp.report_only', false) 
            ? 'Content-Security-Policy-Report-Only' 
            : 'Content-Security-Policy';
        
        $response->headers->set($headerName, $cspHeader);

        // Add other security headers
        $this->addSecurityHeaders($response);

        // Log CSP application in production for debugging
        if (app()->environment('production') && config('app.debug')) {
            Log::info('CSP Applied', [
                'url' => $request->url(),
                'nonce' => $nonce,
                'header_length' => strlen($cspHeader),
            ]);
        }

        return $response;
    }

    /**
     * Build the CSP header string from configuration.
     *
     * @param  array  $config
     * @param  string  $nonce
     * @return string
     */
    protected function buildCspHeader(array $config, string $nonce): string
    {
        $directives = [];

        foreach ($config as $directive => $values) {
            if ($directive === 'upgrade-insecure-requests' && $values === true) {
                $directives[] = 'upgrade-insecure-requests';
                continue;
            }

            if (empty($values)) {
                continue;
            }

            // Replace nonce placeholder with actual nonce
            $processedValues = array_map(function ($value) use ($nonce) {
                return str_replace('%nonce%', $nonce, $value);
            }, $values);

            // Special handling for Livewire and other dynamic content
            // Always ensure nonce is present for style and script directives
            if ($directive === 'style-src' || $directive === 'style-src-elem' || $directive === 'script-src' || $directive === 'script-src-elem') {
                // Ensure we have the nonce for dynamic styles and scripts
                if (!in_array("'nonce-{$nonce}'", $processedValues)) {
                    $processedValues[] = "'nonce-{$nonce}'";
                }
                
                // For development and debugging, also include unsafe-inline
                if (app()->environment('local', 'development', 'testing')) {
                    if (!in_array("'unsafe-inline'", $processedValues)) {
                        $processedValues[] = "'unsafe-inline'";
                    }
                }
            }

            $directives[] = $directive . ' ' . implode(' ', $processedValues);
        }

        // Add report-uri if configured
        if ($reportUri = config('csp.report_uri')) {
            $directives[] = 'report-uri ' . $reportUri;
        }

        return implode('; ', $directives);
    }

    /**
     * Add additional security headers to the response.
     *
     * @param  \Symfony\Component\HttpFoundation\Response  $response
     * @return void
     */
    protected function addSecurityHeaders(Response $response): void
    {
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // Add Permissions-Policy header
        $response->headers->set('Permissions-Policy', 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()');
        
        // Add Strict-Transport-Security header in production
        if (app()->environment('production')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
    }

    /**
     * Determine if CSP should be skipped for this request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function shouldSkipCSP(Request $request): bool
    {
        // Skip CSP for asset files
        $path = $request->path();
        $skipExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'css', 'js', 'pdf', 'ico', 'woff', 'woff2', 'ttf', 'eot'];
        
        foreach ($skipExtensions as $extension) {
            if (preg_match('/\.' . $extension . '$/i', $path)) {
                return true;
            }
        }

        return false;
    }
}