@section('styles')
    <link rel="stylesheet" href="{{ asset('css/purchased-course-detail.css') }}">
@endsection

<x-app-layout>
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Left Sidebar - Course Navigation -->
            <div class="col-lg-3">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0">{{ $course->name }}</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <div class="accordion" id="courseContentAccordion">
                                <div class="accordion-item border-0">
                                    <h2 class="accordion-header" id="headingLectures">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLectures" aria-expanded="true" aria-controls="collapseLectures">
                                            <i class="fas fa-video me-2"></i> Lectures
                                        </button>
                                    </h2>
                                    <div id="collapseLectures" class="accordion-collapse collapse show" aria-labelledby="headingLectures">
                                        <div class="accordion-body p-0">
                                            <div class="list-group list-group-flush">
                                                @foreach($lectures as $lecture)
                                                    <a href="{{ route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id]) }}"
                                                       class="list-group-item list-group-item-action d-flex align-items-center lecture-link">
                                                        <i class="fas fa-play-circle me-2"></i>
                                                        <div>
                                                            <span>{{ $lecture->name }}</span>
                                                            <small class="d-block text-muted">{{ $lecture->duration ?? 'N/A' }}</small>
                                                        </div>
                                                    </a>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">My Courses</a></li>
                        <li class="breadcrumb-item active">{{ $course->name }}</li>
                    </ol>
                </nav>

                <!-- Lecture Player Section -->
                <div id="lecture-player-section" class="card shadow-sm mb-4 lecture-player-hidden">
                    <div class="card-body">
                        <h3 id="lecture-title" class="mb-3"></h3>

                        <!-- Video Player -->
                        <div class="ratio ratio-16x9 mb-4">
                            <iframe id="lecture-video" src="" allowfullscreen></iframe>
                        </div>

                        <!-- Lecture Description -->
                        <div class="lecture-description mt-4">
                            <h4>Description</h4>
                            <div id="lecture-description" class="p-3 bg-light rounded">
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <button id="prev-lecture" class="btn btn-outline-primary nav-btn-hidden">
                                <i class="fas fa-chevron-left me-2"></i> Previous Lecture
                            </button>
                            <button id="next-lecture" class="btn btn-primary nav-btn-hidden">
                                Next Lecture <i class="fas fa-chevron-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Course Overview -->
                <div id="course-overview-section" class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-4 mb-md-0">
                                <img src="{{ $course->image_path ? Storage::url($course->image_path) : 'https://via.placeholder.com/300x200' }}"
                                    alt="{{ $course->name }}"
                                    class="img-fluid rounded mb-3">

                                <div class="d-grid gap-2">
                                    @if($lectures->count() > 0)
                                        <a href="{{ route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lectures->first()->id]) }}"
                                           class="btn btn-primary start-course">
                                            <i class="fas fa-play-circle me-2"></i> Start Learning
                                        </a>
                                    @endif
                                </div>
                            </div>

                            <div class="col-md-8">
                                <h1 class="h3 mb-3">{{ $course->name }}</h1>

                                <div class="mb-4">
                                    <div class="d-flex flex-wrap align-items-center mb-2">
                                        <span class="me-3 mb-2">
                                            <i class="fas fa-book me-1"></i> {{ $lectures->count() }} lectures
                                        </span>
                                        <span class="me-3 mb-2">
                                            <i class="fas fa-clock me-1"></i>
                                            @php
                                                $totalDuration = 0;
                                                foreach($lectures as $lec) {
                                                    if($lec->duration) {
                                                        // Parse duration like "01:51" or "1:51:30"
                                                        $parts = explode(':', $lec->duration);
                                                        if(count($parts) == 2) {
                                                            $totalDuration += (int)$parts[0] * 60 + (int)$parts[1]; // MM:SS
                                                        } elseif(count($parts) == 3) {
                                                            $totalDuration += (int)$parts[0] * 3600 + (int)$parts[1] * 60 + (int)$parts[2]; // HH:MM:SS
                                                        }
                                                    }
                                                }
                                                $hours = floor($totalDuration / 3600);
                                                $minutes = floor(($totalDuration % 3600) / 60);
                                                $totalFormatted = $hours > 0 ? "{$hours}h {$minutes}m" : "{$minutes}m";
                                            @endphp
                                            {{ $totalFormatted }} total
                                        </span>
                                    </div>
                                </div>

                                <div class="course-description mb-4">
                                    <h5 class="mb-3">About This Course</h5>
                                    <div class="p-3 bg-light rounded">
                                        {!! $course->description !!}
                                    </div>
                                </div>

                                <!-- Course Features -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">What You'll Learn</h5>
                                        <ul class="list-group list-group-flush mb-4">
                                            @foreach($course->getFeaturesByType('learn')->take(5) as $feature)
                                                <li class="list-group-item bg-transparent px-0">
                                                    <i class="fas fa-check-circle text-success me-2"></i> {{ $feature->feature_text }}
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>

                                    <div class="col-md-6">
                                        <h5 class="mb-3">Requirements</h5>
                                        <ul class="list-group list-group-flush mb-4">
                                            @foreach($course->getFeaturesByType('requirement')->take(3) as $feature)
                                                <li class="list-group-item bg-transparent px-0">
                                                    <i class="fas fa-circle text-primary me-2"></i> {{ $feature->feature_text }}
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Content Overview -->
                <div id="course-content-section" class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Course Content</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th scope="col">#</th>
                                            <th scope="col">Lecture</th>
                                            <th scope="col">Duration</th>
                                            <th scope="col">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($lectures as $index => $lecture)
                                            <tr>
                                                <th scope="row">{{ $index + 1 }}</th>
                                                <td>{{ $lecture->name }}</td>
                                                <td>{{ $lecture->duration ?? 'N/A' }}</td>
                                                <td>
                                                    <a href="{{ route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id]) }}"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-play-circle me-1"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rating Section -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Rate This Course</h5>
                    </div>
                    <div class="card-body">
                        <div id="user-rating-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="course-rating-stats mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <h3 class="mb-0 me-2" id="average-rating">{{ number_format($course->average_rating, 1) }}</h3>
                                            <div class="star-rating" id="average-star-display" data-rating="{{ $course->average_rating }}">
                                                @for ($i = 1; $i <= 5; $i++)
                                                    @if ($i <= $course->average_rating)
                                                        <i class="fas fa-star text-warning"></i>
                                                    @elseif ($i - 0.5 <= $course->average_rating)
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-warning"></i>
                                                    @endif
                                                @endfor
                                            </div>
                                            <small class="text-muted ms-2">({{ $course->rating_count }} ratings)</small>
                                        </div>
                                    </div>

                                    <!-- Show existing ratings -->
                                    <div id="ratings-container">
                                        <!-- Ratings will be loaded here via Ajax -->
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="rate-course-form">
                                        <h6 class="mb-3">Share your experience with this course</h6>

                                        <div class="rating-input mb-3">
                                            <label class="form-label">Your Rating</label>
                                            <div class="star-rating-input">
                                                <i class="far fa-star fs-4 rating-star" data-value="1"></i>
                                                <i class="far fa-star fs-4 rating-star" data-value="2"></i>
                                                <i class="far fa-star fs-4 rating-star" data-value="3"></i>
                                                <i class="far fa-star fs-4 rating-star" data-value="4"></i>
                                                <i class="far fa-star fs-4 rating-star" data-value="5"></i>
                                                <input type="hidden" id="rating-value" value="0">
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="rating-comment" class="form-label">Your Comment (Optional)</label>
                                            <textarea id="rating-comment" class="form-control" rows="3" placeholder="What did you like or dislike about this course?"></textarea>
                                        </div>

                                        <div class="d-grid">
                                            <button type="button" id="submit-rating" class="btn btn-primary">
                                                <i class="fas fa-star me-2"></i> Submit Rating
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Q&A Section -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Ask a Question</h5>
                    </div>
                    <div class="card-body">
                        @if(!isset($isAdmin) || !$isAdmin)
                        <form id="question-form" class="mb-4">
                                <div class="mb-3">
                                <label for="question-content" class="form-label">Ask a question about this course</label>
                                <textarea class="form-control" id="question-content" rows="3" placeholder="What would you like to know?"></textarea>
                            </div>

                                    <!-- Attachment Options -->
                                    <div class="attachment-options mb-3">
                                        <div class="d-flex gap-3">
                                            <button type="button" id="attach-image-btn" class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-image me-1"></i> Image
                                            </button>
                                            <button type="button" id="attach-pdf-btn" class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-file-pdf me-1"></i> PDF
                                            </button>
                                            <button type="button" id="start-recording-btn" class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-microphone me-1"></i> Record Voice
                                            </button>
                                            <button type="button" id="stop-recording-btn" class="btn btn-outline-danger btn-sm d-none">
                                                <i class="fas fa-stop-circle me-1"></i> Stop Recording
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Hidden File Inputs -->
                                    <input type="file" id="image-upload" accept="image/*" class="d-none">
                                    <input type="file" id="pdf-upload" accept="application/pdf" class="d-none">

                                    <!-- Preview Area -->
                                    <div id="attachments-preview" class="mb-3 d-none">
                                        <h6 class="border-bottom pb-2 mb-3">Attachments</h6>
                                        <div id="attachment-list" class="d-flex flex-wrap gap-3"></div>
                                    </div>

                                <div class="d-flex justify-content-end">
                                <button type="button" id="submit-question" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i> Submit Question
                                    </button>
                                </div>
                        </form>
                        @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> As an admin, you can view and respond to student questions, but cannot submit questions yourself.
                        </div>
                        @endif

                        <div id="questions-container" class="chat-container">
                            <div class="text-center py-4 text-muted" id="no-questions-message">
                                <i class="fas fa-comments fa-2x mb-3"></i>
                                <p>No questions yet about this course. Be the first to ask!</p>
                            </div>
                            <!-- Questions will be dynamically loaded here -->
                        </div>
                            </div>
                        </div>

                <!-- Admin Questions Section - Only visible to admins -->
                @if(isset($isAdmin) && $isAdmin && isset($adminQuestions) && $adminQuestions->count() > 0)
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ Auth::user()->isSuperAdmin() ? 'All Student Questions for this Course' : 'My Assigned Students Questions for this Course' }}</h5>
                    </div>
                    <div class="card-body">
                        @foreach($adminQuestions as $question)
                        <div class="question-card mb-4" id="admin-question-{{ $question->id }}">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ $question->user->name }}</strong>
                                        <span class="text-muted ms-2">{{ $question->created_at->format('M d, Y H:i') }}</span>

                                        @if(Auth::user()->isSuperAdmin())
                                            @php
                                                $adminAssignment = App\Models\AdminUserAssignment::where('user_id', $question->user_id)->first();
                                            @endphp

                                            @if($adminAssignment)
                                                <span class="badge bg-secondary ms-2">
                                                    Assigned to: {{ $adminAssignment->admin->name }}
                                                </span>
                                            @else
                                                <span class="badge bg-warning text-dark ms-2">Unassigned User</span>
                                            @endif
                                        @endif
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ $question->status == 'pending' ? 'warning text-dark' : ($question->status == 'answered' ? 'success' : 'danger') }}">
                                            {{ ucfirst($question->status) }}
                                        </span>
                                        @if($question->lecture)
                                        <a href="{{ route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $question->lecture_id]) }}" class="btn btn-sm btn-info ms-2">
                                            <i class="fas fa-video me-1"></i> View Lecture
                                        </a>
                                        @endif
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="question-content">
                                        <p>{{ $question->content }}</p>

                                        @if($question->attachments && $question->attachments->count() > 0)
                                        <div class="question-attachments mt-3">
                                            <h6 class="mb-2">Attachments:</h6>
                        <div class="row">
                                                @foreach($question->attachments as $attachment)
                                                    @if($attachment->file_type == 'image')
                                                        <div class="col-md-3 mb-2">
                                                            <a href="{{ Storage::url($attachment->file_path) }}" target="_blank">
                                                                <img src="{{ Storage::url($attachment->file_path) }}" alt="Attachment" class="img-thumbnail attachment-thumbnail-img">
                                                            </a>
                                    </div>
                                                    @elseif($attachment->file_type == 'pdf')
                                                        <div class="col-md-3 mb-2">
                                                            <a href="{{ Storage::url($attachment->file_path) }}" target="_blank" class="btn btn-outline-danger">
                                                                <i class="fas fa-file-pdf me-1"></i> {{ $attachment->file_name ?? 'PDF Document' }}
                                                            </a>
                                </div>
                                                    @elseif($attachment->file_type == 'voice')
                                                        <div class="col-md-6 mb-2">
                                                            <audio controls class="w-100">
                                                                <source src="{{ Storage::url($attachment->file_path) }}" type="{{ $attachment->mime_type ?? 'audio/webm' }}">
                                                                Your browser does not support the audio element.
                                                            </audio>
                            </div>
                                                    @endif
                                                @endforeach
                        </div>
                    </div>
                                        @endif
                </div>

                                    <!-- Question metadata -->
                                    @if($question->lecture)
                                    <div class="question-metadata mt-2 mb-3">
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-video me-1"></i> Lecture: {{ $question->lecture->name }}
                                        </span>
            </div>
                                    @endif

                                    <!-- Answers Section -->
                                    @if($question->answers && $question->answers->count() > 0)
                                    <div class="answers-section mt-4">
                                        <h6 class="mb-3">Answers ({{ $question->answers->count() }})</h6>

                                        @foreach($question->answers as $answer)
                                        <div class="answer-card mb-3 {{ $answer->is_pinned ? 'border-start border-success border-3 ps-3' : '' }}">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <strong class="text-primary">{{ $answer->user->name }}</strong>
                                                    <small class="text-muted ms-2">{{ $answer->created_at->diffForHumans() }}</small>
                                                    @if($answer->is_pinned)
                                                        <span class="badge bg-success ms-2">
                                                            <i class="fas fa-thumbtack me-1"></i> Pinned
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <p class="mb-0 mt-2">{{ $answer->content }}</p>

                                            @if($answer->attachments && $answer->attachments->count() > 0)
                                            <div class="answer-attachments mt-2">
                                                @foreach($answer->attachments as $attachment)
                                                    @if($attachment->file_type == 'image')
                                                        <div class="mb-2">
                                                            <a href="{{ Storage::url($attachment->file_path) }}" target="_blank">
                                                                <img src="{{ Storage::url($attachment->file_path) }}" class="img-thumbnail attachment-thumbnail-img">
                                                            </a>
                                                        </div>
                                                    @elseif($attachment->file_type == 'pdf')
                                                        <div class="mb-2">
                                                            <a href="{{ Storage::url($attachment->file_path) }}" target="_blank" class="btn btn-sm btn-outline-danger">
                                                                <i class="fas fa-file-pdf me-1"></i> {{ $attachment->original_name }}
                                                            </a>
                                                        </div>
                                                    @elseif($attachment->file_type == 'voice')
                                                        <div class="mb-2">
                                                            <audio controls>
                                                                <source src="{{ Storage::url($attachment->file_path) }}" type="{{ $attachment->mime_type ?? 'audio/webm' }}">
                                                                Your browser does not support the audio element.
                                                            </audio>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                            @endif
                                        </div>
                                        @endforeach
                                    </div>
                                    @endif

                                    <!-- Answer Form -->
                                    @if($question->status == 'pending')
                                        @livewire('admin.answer-form', ['questionId' => $question->id], key('course-answer-form-'.$question->id))
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
    <style>
        /* Video Protection Styles */
        .video-protected {
            position: relative;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            overflow: hidden;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: transparent;
            z-index: 10;
            cursor: not-allowed;
            pointer-events: none;
        }

        .copy-protected-notice {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            z-index: 20;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .copy-notice-visible {
            opacity: 1;
        }

        .video-watermark {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: rgba(255, 255, 255, 0.7);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            z-index: 15;
            pointer-events: none;
            opacity: 0.7;
            transform: rotate(-10deg);
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
        }

        /* WhatsApp-style Chat UI */
        .chat-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 100%;
            padding: 15px;
            background-color: #e5ddd5;
            background-image: url("data:image/svg+xml,%3Csvg width='90' height='90' viewBox='0 0 90 90' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23d9d4cd' fill-opacity='0.4' fill-rule='evenodd'%3E%3Cpath d='M0 0h90v90H0z'/%3E%3Cpath d='M0 0h45v45H0z'/%3E%3Cpath d='M45 45h45v45H45z'/%3E%3C/g%3E%3C/svg%3E");
            border-radius: 8px;
        }

        /* Conversation groups */
        .conversation-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            position: relative;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.1);
            background-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        /* Alternating conversation colors */
        .conversation-group:nth-child(odd) {
            background-color: rgba(255,255,255,0.7);
        }

        .conversation-group:nth-child(even) {
            background-color: rgba(255,255,255,0.3);
        }

        /* Date header for each conversation */
        .conversation-date {
            text-align: center;
            font-size: 0.75rem;
            margin-bottom: 10px;
            padding: 5px 10px;
            background-color: rgba(0,0,0,0.1);
            border-radius: 10px;
            color: #333;
            align-self: center;
            font-weight: 500;
        }

        .chat-message {
            display: flex;
            flex-direction: column;
            max-width: 70%;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chat-message.question {
            align-self: flex-start;
        }

        .chat-message.answer {
            align-self: flex-end;
        }

        .chat-bubble {
            padding: 10px 14px;
            border-radius: 10px;
            position: relative;
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            word-wrap: break-word;
        }

        .chat-message.question .chat-bubble {
            background-color: #ffffff;
            border-top-left-radius: 0;
        }

        .chat-message.question .chat-bubble:before {
            content: "";
            position: absolute;
            top: 0;
            left: -10px;
            width: 0;
            height: 0;
            border-top: 10px solid #ffffff;
            border-left: 10px solid transparent;
        }

        .chat-message.answer .chat-bubble {
            background-color: #dcf8c6;
            border-top-right-radius: 0;
            text-align: left;
        }

        .chat-message.answer .chat-bubble:before {
            content: "";
            position: absolute;
            top: 0;
            right: -10px;
            width: 0;
            height: 0;
            border-top: 10px solid #dcf8c6;
            border-right: 10px solid transparent;
        }

        .chat-meta {
            display: flex;
            font-size: 0.7rem;
            color: #6c757d;
            margin-bottom: 2px;
            padding: 0 5px;
        }

        .chat-message.answer .chat-meta {
            justify-content: flex-end;
        }

        .chat-attachments {
            margin-top: 8px;
        }

        /* Timestamp style */
        .chat-time {
            font-size: 0.65rem;
            color: #888;
            align-self: flex-end;
            margin-top: 2px;
            margin-right: 5px;
        }

        /* Pinned answer styles */
        .chat-message.answer.pinned .chat-bubble {
            background-color: #c6e2f8;
            border-left: 2px solid #3498db;
        }

        .chat-message.answer.pinned .chat-bubble:before {
            border-top-color: #c6e2f8;
        }

        /* Status indicators */
        .status-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 0.65rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-answered {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* Mobile responsive */
        @media (max-width: 576px) {
            .chat-message {
                max-width: 85%;
            }
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Apply advanced video protection
            applyAdvancedVideoProtection();

            // Load questions
            loadQuestions();
        });

        // Function to apply advanced video protection
        function applyAdvancedVideoProtection() {
            // Find all video containers
            const videoContainers = document.querySelectorAll('.ratio-16x9');
            if (videoContainers.length === 0) return;

            videoContainers.forEach(container => {
                // Add protection class
                container.classList.add('video-protected');

                // Create overlay for click protection
                const overlay = document.createElement('div');
                overlay.className = 'video-overlay';
                container.appendChild(overlay);

                // Add user watermark
                const watermark = document.createElement('div');
                watermark.className = 'video-watermark';
                watermark.textContent = '{{ auth()->user()->email }}';
                container.appendChild(watermark);

                // Create copy protection notice
                const notice = document.createElement('div');
                notice.className = 'copy-protected-notice';
                notice.textContent = 'Content is protected. Copying is not allowed.';
                container.appendChild(notice);

                // Disable right-click on video container
                container.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    showCopyProtectionNotice(notice);
                    return false;
                });

                // Disable drag on iframe or video
                const mediaElement = container.querySelector('iframe') || container.querySelector('video');
                if (mediaElement) {
                    mediaElement.addEventListener('dragstart', function(e) {
                        e.preventDefault();
                        showCopyProtectionNotice(notice);
                        return false;
                    });

                    // Apply specific protection to iframe
                    if (mediaElement.tagName === 'IFRAME') {
                        // Add sandbox attributes to prevent downloads
                        mediaElement.setAttribute('sandbox', 'allow-same-origin allow-scripts');

                        // Ensure no download option is available
                        mediaElement.setAttribute('allow', 'accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture');
                        mediaElement.setAttribute('disablePictureInPicture', 'true');
                    }

                    // Apply specific protection to HTML5 video
                    if (mediaElement.tagName === 'VIDEO') {
                        mediaElement.setAttribute('controlsList', 'nodownload nofullscreen noremoteplayback');
                        mediaElement.setAttribute('disablePictureInPicture', 'true');
                        mediaElement.setAttribute('oncontextmenu', 'return false;');
                    }
                }
            });

            // Global keyboard shortcut prevention
            document.addEventListener('keydown', function(e) {
                // Check if any video element is currently visible
                const videoVisible = Array.from(videoContainers).some(container => {
                    const rect = container.getBoundingClientRect();
                    return (
                        rect.top >= 0 &&
                        rect.left >= 0 &&
                        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                    );
                });

                if (!videoVisible) return;

                // Prevent common screenshot/recording shortcuts
                if (
                    e.key === 'PrintScreen' ||
                    (e.ctrlKey && e.key === 'p') || // Print
                    (e.ctrlKey && e.key === 's') || // Save
                    (e.ctrlKey && e.key === 'c') || // Copy
                    (e.ctrlKey && e.shiftKey && e.key === 'i') || // Developer tools
                    (e.ctrlKey && e.shiftKey && e.key === 'c') || // Element inspect
                    e.key === 'F12' // Developer tools
                ) {
                    e.preventDefault();
                    const notices = document.querySelectorAll('.copy-protected-notice');
                    if (notices.length > 0) {
                        showCopyProtectionNotice(notices[0]);
                    }
                    return false;
                }
            });

            // Disable browser developer tools (best effort)
            // This won't stop dedicated users but provides a barrier
            document.addEventListener('devtoolschange', function(e) {
                if (e.detail.isOpen) {
                    alert('Developer tools are not allowed while viewing course content.');
                }
            });
        }

        // Function to show copy protection notice
        function showCopyProtectionNotice(notice) {
            notice.classList.add('copy-notice-visible');
            setTimeout(() => {
                notice.classList.remove('copy-notice-visible');
            }, 3000);
        }

            // Store lecture data
            let lectures = @json($lectures);
            let currentLectureIndex = -1;

            // Store attachments
            let attachments = {
                images: [],
                pdfs: [],
                voice: null
            };
            let mediaRecorder = null;
            let audioChunks = [];

            // Function to load lecture content
            function loadLecture(courseId, lectureId) {
                // Show loading state
                document.getElementById('lecture-title').innerText = 'Loading...';
                document.getElementById('lecture-description').innerHTML = '<p>Loading lecture content...</p>';
                document.getElementById('lecture-video').src = '';

                // Show lecture player section, hide course overview
                document.getElementById('lecture-player-section').style.display = 'block';
                document.getElementById('course-overview-section').style.display = 'none';
                document.getElementById('course-content-section').style.display = 'none';

                // Find current lecture index
                currentLectureIndex = lectures.findIndex(lecture => lecture.id == lectureId);

                // Fetch lecture content via AJAX
                fetch(`/my-course/${courseId}/lecture-content/${lectureId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update lecture content
                            document.getElementById('lecture-title').innerText = data.lecture.name;
                            document.getElementById('lecture-description').innerHTML = data.description;

                            if (data.videoUrl) {
                                document.getElementById('lecture-video').src = data.videoUrl;
                            } else {
                                document.getElementById('lecture-video').innerHTML = '<div class="d-flex align-items-center justify-content-center bg-light h-100"><p class="text-muted">No video available for this lecture</p></div>';
                            }

                            // Update navigation buttons
                            updateNavigationButtons();

                            // Highlight current lecture in sidebar
                            document.querySelectorAll('.lecture-link').forEach(link => {
                                link.classList.remove('active');
                                if (link.dataset.lectureId == lectureId) {
                                    link.classList.add('active');
                                }
                            });

                            // Scroll to top
                            window.scrollTo(0, 0);
                        } else {
                            alert('Error loading lecture: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while loading the lecture.');
                    });
            }

            // Update navigation buttons based on current lecture index
            function updateNavigationButtons() {
                const prevButton = document.getElementById('prev-lecture');
                const nextButton = document.getElementById('next-lecture');

                // Previous button
                if (currentLectureIndex > 0) {
                    prevButton.style.display = 'block';
                    prevButton.dataset.lectureId = lectures[currentLectureIndex - 1].id;
                } else {
                    prevButton.style.display = 'none';
                }

                // Next button
                if (currentLectureIndex < lectures.length - 1) {
                    nextButton.style.display = 'block';
                    nextButton.dataset.lectureId = lectures[currentLectureIndex + 1].id;
                } else {
                    nextButton.style.display = 'none';
                }
            }

            // Event listeners for lecture links
        // Note: These event listeners are no longer needed as links now directly navigate to lecture pages
        /*
            document.querySelectorAll('.lecture-link').forEach(link => {
                link.addEventListener('click', function() {
                    const lectureId = this.dataset.lectureId;
                    const courseId = this.dataset.courseId;
                    loadLecture(courseId, lectureId);
                });
            });
        */

        // Event listeners for Start Learning button
        // Note: These event listeners are no longer needed as the button now directly navigates to the first lecture
        /*
            document.querySelectorAll('.start-course').forEach(button => {
                button.addEventListener('click', function() {
                    const lectureId = this.dataset.lectureId;
                    const courseId = this.dataset.courseId;
                    loadLecture(courseId, lectureId);
                });
            });
        */

            // Event listeners for navigation buttons
            document.getElementById('prev-lecture').addEventListener('click', function() {
                const lectureId = this.dataset.lectureId;
                const courseId = document.querySelectorAll('.lecture-link')[0].dataset.courseId;
                loadLecture(courseId, lectureId);
            });

            document.getElementById('next-lecture').addEventListener('click', function() {
                const lectureId = this.dataset.lectureId;
                const courseId = document.querySelectorAll('.lecture-link')[0].dataset.courseId;
                loadLecture(courseId, lectureId);
            });

            // Q&A Functionality
            const courseId = {{ $course->id }};
            let currentLectureId = null;

            // Load questions for the course
            loadQuestions();

            // Attach image event listener
            document.getElementById('attach-image-btn').addEventListener('click', function() {
                document.getElementById('image-upload').click();
            });

            // Attach PDF event listener
            document.getElementById('attach-pdf-btn').addEventListener('click', function() {
                document.getElementById('pdf-upload').click();
            });

            // Image upload handling
            document.getElementById('image-upload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('Image file is too large. Maximum allowed size is 5MB.');
                        return;
                    }

                    attachments.images.push(file);
                    updateAttachmentPreview();
                }
            });

            // PDF upload handling
            document.getElementById('pdf-upload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    if (file.size > 10 * 1024 * 1024) { // 10MB limit
                        alert('PDF file is too large. Maximum allowed size is 10MB.');
                        return;
                    }

                    attachments.pdfs.push(file);
                    updateAttachmentPreview();
                }
            });

            // Voice recording handling
            document.getElementById('start-recording-btn').addEventListener('click', function() {
                // Check if browser supports audio recording
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    alert('Your browser does not support audio recording.');
                    return;
                }

                // Special handling for Chrome in local environment
                const isLocalhost = window.location.hostname === 'localhost' ||
                                   window.location.hostname === '127.0.0.1' ||
                                   window.location.hostname.includes('192.168.');

                const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);

                if (isLocalhost && isChrome && window.location.protocol !== 'https:') {
                    console.warn('Audio recording may not work on Chrome in insecure contexts. If you encounter errors, try using Firefox or Edge for local development, or add a self-signed SSL certificate.');
                }

                // Enable required audio constraints
                const constraints = {
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                };

                // Request audio permission with specific constraints
                navigator.mediaDevices.getUserMedia(constraints)
                    .then(stream => {
                        try {
                            // Hide start button, show stop button
                            document.getElementById('start-recording-btn').classList.add('d-none');
                            document.getElementById('stop-recording-btn').classList.remove('d-none');

                            // Initialize media recorder with specific settings
                            const options = { mimeType: 'audio/webm' };
                            try {
                                mediaRecorder = new MediaRecorder(stream, options);
                            } catch (e) {
                                // Fallback if preferred format not supported
                                console.log('Using default MediaRecorder format');
                                mediaRecorder = new MediaRecorder(stream);
                            }

                            audioChunks = [];

                            // Collect audio chunks
                            mediaRecorder.addEventListener('dataavailable', event => {
                                if (event.data.size > 0) {
                                    audioChunks.push(event.data);
                                }
                            });

                            // When recording stops
                            mediaRecorder.addEventListener('stop', () => {
                                try {
                                    // Use the correct MIME type based on what the MediaRecorder actually created
                                    // Most browsers support audio/webm or audio/ogg
                                    const recordedMimeType = mediaRecorder.mimeType || 'audio/webm';
                                    const fileExtension = recordedMimeType.includes('webm') ? 'webm' :
                                                         recordedMimeType.includes('ogg') ? 'ogg' : 'wav';

                                    // Create blob from audio chunks with the MIME type that was actually used
                                    const audioBlob = new Blob(audioChunks, { type: recordedMimeType });

                                    // Use the correct file extension and MIME type
                                    attachments.voice = new File([audioBlob], `voice-note.${fileExtension}`, {
                                        type: recordedMimeType
                                    });

                                    console.log('Audio recording created with MIME type:', recordedMimeType);
                                    console.log('File created with name:', attachments.voice.name);

                                    // Update preview
                                    updateAttachmentPreview();

                                    // Clean up
                                    stream.getTracks().forEach(track => track.stop());
                                } catch (err) {
                                    console.error('Error creating audio file:', err);
                                    alert('Error creating audio recording. Please try again.');
                                }
                            });

                            // Set a timeout of 10ms before starting recording
                            // This helps avoid some browser bugs
                            setTimeout(() => {
                                // Start recording with 10ms timeslice for regular chunks
                                mediaRecorder.start(10);
                                console.log('MediaRecorder started', mediaRecorder.state);
                            }, 10);
                        } catch (err) {
                            console.error('Error initializing media recorder:', err);
                            alert('Error initializing audio recording. Please try again.');

                            // Reset UI
                            document.getElementById('start-recording-btn').classList.remove('d-none');
                            document.getElementById('stop-recording-btn').classList.add('d-none');

                            // Clean up
                            stream.getTracks().forEach(track => track.stop());
                        }
                    })
                    .catch(error => {
                        console.error('Error accessing microphone:', error);
                        let errorMessage = 'Failed to access microphone. ';

                        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                            errorMessage += 'Please ensure you have given permission to use the microphone.';
                            if (isLocalhost) {
                                errorMessage += '\n\nIn Chrome, microphone access may be blocked in non-HTTPS local environments. Try using Firefox or Edge instead.';
                            }
                        } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
                            errorMessage += 'No microphone device found. Please connect a microphone.';
                        } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
                            errorMessage += 'Microphone is already in use by another application.';
                        } else if (error.name === 'OverconstrainedError') {
                            errorMessage += 'Microphone constraints cannot be satisfied.';
                        } else if (error.name === 'SecurityError') {
                            errorMessage += 'Use of microphone is not allowed in this context. Please ensure you are using HTTPS.';
                            if (isLocalhost) {
                                errorMessage += '\n\nFor local development, try using Firefox which allows microphone access in non-HTTPS contexts.';
                            }
                        } else {
                            errorMessage += 'Please ensure you are using HTTPS and have given permission.';
                            if (isLocalhost) {
                                errorMessage += '\n\nFor local development, this might be a browser security limitation.';
                            }
                        }

                        alert(errorMessage);
                    });
            });

            // Stop recording
            document.getElementById('stop-recording-btn').addEventListener('click', function() {
                if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                    try {
                        mediaRecorder.stop();
                    } catch (err) {
                        console.error('Error stopping recording:', err);
                    }
                    document.getElementById('stop-recording-btn').classList.add('d-none');
                    document.getElementById('start-recording-btn').classList.remove('d-none');
                }
            });

            // Update attachment preview
            function updateAttachmentPreview() {
                const previewArea = document.getElementById('attachments-preview');
                const attachmentList = document.getElementById('attachment-list');

                // Clear previous preview
                attachmentList.innerHTML = '';

                // Check if we have any attachments
                const hasAttachments = attachments.images.length > 0 ||
                                       attachments.pdfs.length > 0 ||
                                       attachments.voice !== null;

                if (hasAttachments) {
                    previewArea.classList.remove('d-none');

                    // Add image previews
                    attachments.images.forEach((image, index) => {
                        const imgPreview = document.createElement('div');
                        imgPreview.className = 'attachment-preview';
                        imgPreview.innerHTML = `
                            <div class="card attachment-card">
                                <img src="${URL.createObjectURL(image)}" class="card-img-top attachment-img">
                                <div class="card-footer p-1">
                                    <button type="button" class="btn btn-sm btn-danger w-100 remove-attachment" data-type="image" data-index="${index}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                        attachmentList.appendChild(imgPreview);
                    });

                    // Add PDF previews
                    attachments.pdfs.forEach((pdf, index) => {
                        const pdfPreview = document.createElement('div');
                        pdfPreview.className = 'attachment-preview';
                        pdfPreview.innerHTML = `
                            <div class="card attachment-card">
                                <div class="card-body p-2 text-center">
                                    <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                    <p class="mb-0 small text-truncate">${pdf.name}</p>
                                </div>
                                <div class="card-footer p-1">
                                    <button type="button" class="btn btn-sm btn-danger w-100 remove-attachment" data-type="pdf" data-index="${index}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                        attachmentList.appendChild(pdfPreview);
                    });

                    // Add voice preview
                    if (attachments.voice) {
                        const voicePreview = document.createElement('div');
                        voicePreview.className = 'attachment-preview';
                        voicePreview.innerHTML = `
                            <div class="card attachment-card-wide">
                                <div class="card-body p-2">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-microphone text-primary"></i>
                                        <audio controls class="w-100">
                                            <source src="${URL.createObjectURL(attachments.voice)}" type="${attachments.voice.type}">
                                            Your browser does not support the audio element.
                                        </audio>
                                    </div>
                                </div>
                                <div class="card-footer p-1">
                                    <button type="button" class="btn btn-sm btn-danger w-100 remove-attachment" data-type="voice">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                        attachmentList.appendChild(voicePreview);
                    }

                    // Add event listeners to remove buttons
                    document.querySelectorAll('.remove-attachment').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const type = this.dataset.type;
                            if (type === 'image') {
                                attachments.images.splice(parseInt(this.dataset.index), 1);
                            } else if (type === 'pdf') {
                                attachments.pdfs.splice(parseInt(this.dataset.index), 1);
                            } else if (type === 'voice') {
                                attachments.voice = null;
                            }
                            updateAttachmentPreview();
                        });
                    });

                } else {
                    previewArea.classList.add('d-none');
                }
            }

            // Submit question
            document.getElementById('submit-question').addEventListener('click', function() {
                const content = document.getElementById('question-content').value.trim();

                if (!content && !attachments.images.length && !attachments.pdfs.length && !attachments.voice) {
                    alert('Please enter your question or add an attachment');
                    return;
                }

                // Disable button during submission
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';

                // Create FormData object for file uploads
                const formData = new FormData();
                formData.append('content', content);
                formData.append('course_id', courseId);
                if (currentLectureId) {
                    formData.append('lecture_id', currentLectureId);
                }

                // Add attachments
                attachments.images.forEach((image, index) => {
                    formData.append(`images[${index}]`, image);
                });

                attachments.pdfs.forEach((pdf, index) => {
                    formData.append(`pdfs[${index}]`, pdf);
                });

                if (attachments.voice) {
                    formData.append('voice', attachments.voice);
                }

                // Send AJAX request to submit question
                fetch('{{ route('questions.store') }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear textarea and attachments
                        document.getElementById('question-content').value = '';
                        attachments = {
                            images: [],
                            pdfs: [],
                            voice: null
                        };
                        updateAttachmentPreview();

                        // Show success message
                        alert('Your question has been submitted successfully!');

                        // Reload questions
                        loadQuestions();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while submitting your question.');
                })
                .finally(() => {
                    // Re-enable button
                    const btn = document.getElementById('submit-question');
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-paper-plane me-2"></i> Submit Question';
                });
            });

            // Function to load questions
            function loadQuestions() {
                fetch('{{ route('questions.get') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        course_id: courseId,
                        lecture_id: currentLectureId,
                        user_id: {{ auth()->id() }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const container = document.getElementById('questions-container');
                        const noQuestionsMsg = document.getElementById('no-questions-message');

                        if (data.questions.length > 0) {
                            // Hide the "no questions" message
                            noQuestionsMsg.style.display = 'none';

                        // Sort questions by created_at in descending order (newest first)
                        data.questions.sort((a, b) => {
                            const dateA = new Date(a.created_at.replace(/(\d+)(st|nd|rd|th)/, "$1"));
                            const dateB = new Date(b.created_at.replace(/(\d+)(st|nd|rd|th)/, "$1"));
                            return dateB - dateA; // Newest first
                        });

                            // Generate HTML for questions and answers
                            let html = '';

                            data.questions.forEach(question => {
                            // Create conversation container for this question and its answers
                            html += '<div class="conversation-group mb-4">';

                            // Add status indicator
                            html += `<div class="status-indicator status-${question.status.toLowerCase()}">${formatStatus(question.status)}</div>`;

                            // Add date header
                            const formattedDate = formatDate(question.created_at);
                            html += `<div class="conversation-date">${formattedDate}</div>`;

                                // Prepare attachment previews
                                let attachmentsHtml = '';
                                if (question.attachments && question.attachments.length > 0) {
                                attachmentsHtml += '<div class="chat-attachments">';

                                    question.attachments.forEach(attachment => {
                                        if (attachment.type === 'image') {
                                            attachmentsHtml += `
                                                <div class="mb-2">
                                                    <a href="${attachment.url}" target="_blank">
                                                        <img src="${attachment.url}" class="img-thumbnail attachment-thumbnail-img">
                                                    </a>
                                                </div>`;
                                        } else if (attachment.type === 'pdf') {
                                            attachmentsHtml += `
                                                <div class="mb-2">
                                                    <a href="${attachment.url}" target="_blank" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-file-pdf me-1"></i> ${attachment.name}
                                                    </a>
                                                </div>`;
                                        } else if (attachment.type === 'voice') {
                                            attachmentsHtml += `
                                                <div class="mb-2">
                                                    <audio controls>
                                                        <source src="${attachment.url}" type="${attachment.mime_type || 'audio/webm'}">
                                                        Your browser does not support the audio element.
                                                    </audio>
                                                </div>`;
                                        }
                                    });

                                    attachmentsHtml += '</div>';
                                }

                            // Create question chat bubble
                                html += `
                            <div class="chat-message question">
                                <div class="chat-meta">
                                    <span class="user-name">${question.user}</span>
                                    <span class="badge ms-2 bg-${getStatusBadgeColor(question.status)}">${formatStatus(question.status)}</span>
                                            </div>
                                <div class="chat-bubble">
                                            <p class="mb-0">${question.content}</p>
                                            ${attachmentsHtml}
                                    <div class="chat-time">${formatTime(question.created_at)}</div>
                                    </div>
                                </div>`;

                            // Add answers if any
                            if (question.answers && question.answers.length > 0) {
                                html += renderAnswers(question.answers);
                            }

                            // Close the conversation group
                            html += '</div>';
                            });

                            container.innerHTML = html;
                        } else {
                            // Show the "no questions" message
                            noQuestionsMsg.style.display = 'block';
                        }
                    } else {
                        console.error('Error loading questions:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }

            // Helper function to render answers
            function renderAnswers(answers) {
            let html = '';

                // Sort answers to show pinned answers first
                answers.sort((a, b) => {
                    if (a.is_pinned && !b.is_pinned) return -1;
                    if (!a.is_pinned && b.is_pinned) return 1;
                    return 0;
                });

                answers.forEach(answer => {
                    // Prepare attachments for answers if any
                    let attachmentsHtml = '';
                    if (answer.attachments && answer.attachments.length > 0) {
                    attachmentsHtml += '<div class="chat-attachments">';

                        answer.attachments.forEach(attachment => {
                            if (attachment.type === 'image') {
                                attachmentsHtml += `
                                    <div class="mb-2">
                                        <a href="${attachment.url}" target="_blank">
                                            <img src="${attachment.url}" class="img-thumbnail attachment-thumbnail-img">
                                        </a>
                                    </div>`;
                            } else if (attachment.type === 'pdf') {
                                attachmentsHtml += `
                                    <div class="mb-2">
                                        <a href="${attachment.url}" target="_blank" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-file-pdf me-1"></i> ${attachment.name}
                                        </a>
                                    </div>`;
                            } else if (attachment.type === 'voice') {
                                attachmentsHtml += `
                                    <div class="mb-2">
                                        <audio controls>
                                            <source src="${attachment.url}" type="${attachment.mime_type || 'audio/webm'}">
                                            Your browser does not support the audio element.
                                        </audio>
                                    </div>`;
                            }
                        });

                        attachmentsHtml += '</div>';
                    }

                // Create answer chat bubble
                    html += `
                <div class="chat-message answer ${answer.is_pinned ? 'pinned' : ''}">
                    <div class="chat-meta">
                        ${answer.is_pinned ? '<span class="badge bg-success me-2"><i class="fas fa-thumbtack me-1"></i> Pinned</span>' : ''}
                        <span class="user-name">${answer.user}</span>
                            </div>
                    <div class="chat-bubble">
                        <p class="mb-0">${answer.content}</p>
                        ${attachmentsHtml}
                        <div class="chat-time">${formatTime(answer.created_at)}</div>
                    </div>
                    </div>`;
                });

                return html;
            }

            // Helper function to get badge color based on status
            function getStatusBadgeColor(status) {
                switch(status) {
                    case 'pending': return 'warning';
                    case 'answered': return 'success';
                    case 'rejected': return 'danger';
                    default: return 'secondary';
                }
            }

            // Helper function to format status text
            function formatStatus(status) {
                return status.charAt(0).toUpperCase() + status.slice(1);
            }

        // Helper function to format time in WhatsApp style
        function formatTime(dateStr) {
            // If the date string is already short (like "2 hours ago"), just return it
            if (dateStr && dateStr.length < 20) {
                return dateStr;
            }

            try {
                // Try to parse the date - first remove any ordinal suffixes that might cause issues
                const cleanStr = dateStr.replace(/(\d+)(st|nd|rd|th)/, "$1");

                // Create a date object - this works with ISO strings and many other formats
                const date = new Date(cleanStr);

                // Check if the date is valid
                if (isNaN(date.getTime())) {
                    // If we can't parse it, return the original string
                    return dateStr;
                }

                // Format time as HH:MM (using locale settings)
                return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            } catch (e) {
                console.log('Date parsing error:', e);
                // In case of any error, return the original string
                return dateStr;
            }
        }

        // Helper function to format date in WhatsApp style
        function formatDate(dateStr) {
            // If the date string is already short (like "2 hours ago"), just return it
            if (dateStr && dateStr.length < 20) {
                return dateStr;
            }

            try {
                // Try to parse the date - first remove any ordinal suffixes that might cause issues
                const cleanStr = dateStr.replace(/(\d+)(st|nd|rd|th)/, "$1");

                // Create a date object - this works with ISO strings and many other formats
                const date = new Date(cleanStr);

                // Check if the date is valid
                if (isNaN(date.getTime())) {
                    // If we can't parse it, return the original string
                    return dateStr;
                }

                // Format date as MMM d, yyyy
                return date.toLocaleDateString([], {month: 'short', day: 'numeric', year: 'numeric'});
            } catch (e) {
                console.log('Date parsing error:', e);
                // In case of any error, return the original string
                return dateStr;
            }
        }

        // Rating functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize rating stars
            const ratingStars = document.querySelectorAll('.rating-star');
            const ratingValue = document.getElementById('rating-value');
            const submitButton = document.getElementById('submit-rating');

            // Load existing ratings
            loadRatings();

            // Handle star rating selection
            ratingStars.forEach(star => {
                star.addEventListener('mouseover', function() {
                    const value = parseInt(this.dataset.value);
                    highlightStars(value);
                });

                star.addEventListener('mouseleave', function() {
                    const selectedValue = parseInt(ratingValue.value);
                    highlightStars(selectedValue);
                });

                star.addEventListener('click', function() {
                    const value = parseInt(this.dataset.value);
                    ratingValue.value = value;
                    highlightStars(value);
                });
            });

            // Function to highlight stars
            function highlightStars(count) {
                ratingStars.forEach(star => {
                    const starValue = parseInt(star.dataset.value);
                    if (starValue <= count) {
                        star.classList.remove('far');
                        star.classList.add('fas');
                        star.classList.add('text-warning');
                    } else {
                        star.classList.remove('fas');
                        star.classList.remove('text-warning');
                        star.classList.add('far');
                    }
                });
            }

            // Submit rating
            if (submitButton) {
                submitButton.addEventListener('click', function() {
                    const rating = parseInt(ratingValue.value);
                    const comment = document.getElementById('rating-comment').value.trim();

                    if (rating === 0) {
                        alert('Please select a rating before submitting.');
                        return;
                    }

                    // Disable button during submission
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';

                    // Get CSRF token
                    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                    // Send rating to server
                    fetch('{{ route('courses.rate', ['course' => $course->id]) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': token,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            rating: rating,
                            comment: comment
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert(data.message);

                            // Update average rating display
                            const avgRating = document.getElementById('average-rating');
                            avgRating.textContent = parseFloat(data.average_rating).toFixed(1);

                            // Update rating count
                            const ratingStatsElement = document.querySelector('.course-rating-stats .text-muted');
                            ratingStatsElement.textContent = `(${data.rating_count} ratings)`;

                            // Update average stars display
                            const averageStarDisplay = document.getElementById('average-star-display');
                            updateAverageStarDisplay(data.average_rating);

                            // Reload ratings
                            loadRatings();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while submitting your rating.');
                    })
                    .finally(() => {
                        // Re-enable button
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="fas fa-star me-2"></i> Submit Rating';
                    });
                });
            }

            // Function to update average star display
            function updateAverageStarDisplay(rating) {
                const stars = document.querySelectorAll('#average-star-display i');
                stars.forEach((star, index) => {
                    // Clear all classes first
                    star.className = '';

                    if (index + 1 <= rating) {
                        // Full star
                        star.className = 'fas fa-star text-warning';
                    } else if (index + 0.5 <= rating) {
                        // Half star
                        star.className = 'fas fa-star-half-alt text-warning';
                    } else {
                        // Empty star
                        star.className = 'far fa-star text-warning';
                    }
                });
            }

            // Function to load ratings
            function loadRatings() {
                // Get CSRF token
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Get ratings container
                const ratingsContainer = document.getElementById('ratings-container');

                // Show loading indicator
                ratingsContainer.innerHTML = `
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading ratings...</p>
                    </div>
                `;

                // Fetch ratings
                fetch('{{ route('courses.ratings', ['course' => $course->id]) }}')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (data.ratings.length > 0) {
                                // Generate HTML for ratings
                                let html = '<div class="ratings-list mt-4">';

                                data.ratings.forEach(rating => {
                                    // Format date
                                    const date = new Date(rating.created_at);
                                    const formattedDate = date.toLocaleDateString([], {year: 'numeric', month: 'short', day: 'numeric'});

                                    html += `
                                        <div class="rating-item mb-3 p-3 border-bottom">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <div>
                                                    <strong>${rating.user.name}</strong>
                                                    <div class="d-inline-block ms-2">
                                                    `;

                                    // Add stars
                                    for (let i = 1; i <= 5; i++) {
                                        if (i <= rating.rating) {
                                            html += '<i class="fas fa-star text-warning small"></i>';
                                        } else {
                                            html += '<i class="far fa-star text-warning small"></i>';
                                        }
                                    }

                                    html += `
                                                    </div>
                                                </div>
                                                <div>
                                                    <small class="text-muted">${formattedDate}</small>
                                                </div>
                                            </div>
                                    `;

                                    // Add comment if exists
                                    if (rating.comment) {
                                        html += `<p class="mb-0 text-muted">${rating.comment}</p>`;
                                    }

                                    html += `</div>`;
                                });

                                html += '</div>';

                                // Update container
                                ratingsContainer.innerHTML = html;
                            } else {
                                // Show no ratings message
                                ratingsContainer.innerHTML = `
                                    <div class="text-center py-3">
                                        <p class="text-muted">No ratings yet. Be the first to rate this course!</p>
                                    </div>
                                `;
                            }
                        } else {
                            ratingsContainer.innerHTML = `
                                <div class="alert alert-warning">
                                    Failed to load ratings.
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        ratingsContainer.innerHTML = `
                            <div class="alert alert-danger">
                                An error occurred while loading ratings.
                            </div>
                        `;
                    });
            }
        });
    </script>
    @endpush
</x-app-layout>
