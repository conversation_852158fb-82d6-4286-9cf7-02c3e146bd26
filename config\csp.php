<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Content Security Policy Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the Content Security Policy (CSP) configuration for
    | the application. CSP helps prevent Cross-Site Scripting (XSS) and other
    | code injection attacks by controlling which resources can be loaded.
    |
    */

    // Enable or disable CSP
    'enabled' => env('CSP_ENABLED', true),

    // Enable CSP reporting
    'report_only' => env('CSP_REPORT_ONLY', false),

    // URI where CSP violations will be reported
    'report_uri' => '/api/csp-report',

    // Enable automatic nonce generation for scripts and styles
    'nonce' => [
        'enabled' => true,
        'script' => true,
        'style' => true,
    ],
    
    // Special handling for Livewire
    'livewire' => [
        'enabled' => true,
        'allow_scripts' => true,
        'allow_styles' => true,
    ],

    // CSP directives for production environment (strict - no unsafe directives)
    'production' => [
        'default-src' => ["'self'"],
        'script-src' => [
            "'self'",
            // External script domains
            'https://cdn.jsdelivr.net',
            'https://code.jquery.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            'https://www.youtube.com',
            'https://youtube.com',
            'https://www.youtube-nocookie.com',
            'https://maps.googleapis.com',
            // Allow unsafe-inline for bootstrap and other core libraries
            "'unsafe-inline'",
            // Allow unsafe-eval for Livewire
            "'unsafe-eval'",
            // Nonce for dynamic scripts
            "'nonce-%nonce%'",
        ],
        'script-src-elem' => [
            "'self'",
            // External script domains
            'https://cdn.jsdelivr.net',
            'https://code.jquery.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            'https://www.youtube.com',
            'https://youtube.com',
            'https://www.youtube-nocookie.com',
            'https://maps.googleapis.com',
            // Nonce for dynamic scripts - unsafe-inline is ignored when nonce is present
            "'nonce-%nonce%'",
        ],
        'style-src' => [
            "'self'",
            // External style domains
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://use.fontawesome.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            // Allow unsafe-inline for bootstrap and other core libraries
            "'unsafe-inline'",
            // Nonce for dynamic styles
            "'nonce-%nonce%'",
        ],
        'style-src-elem' => [
            "'self'",
            // External style domains
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://use.fontawesome.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            // Temporary: Allow unsafe-inline for debugging
            "'unsafe-inline'",
            // Nonce for dynamic styles - unsafe-inline is ignored when nonce is present
            "'nonce-%nonce%'",
        ],
        'font-src' => [
            "'self'",
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://use.fontawesome.com',
            'data:',
        ],
        'img-src' => [
            "'self'",
            'data:',
            'https:',
            'blob:',
        ],
        'media-src' => [
            "'self'",
            'data:',
            'blob:',
        ],
        'connect-src' => [
            "'self'",
            'https://api.ipify.org',
            'https://noembed.com',
            'https://cdn.plyr.io',
            'https://api.github.com',
            'https://maps.googleapis.com',
            'wss://*.livewire.io', // For Livewire WebSocket connections
            'https://*.livewire.io', // For Livewire HTTP connections
        ],
        'frame-src' => [
            "'self'",
            'https://www.youtube.com',
            'https://youtube.com',
            'https://youtu.be',
            'https://www.youtube-nocookie.com',
        ],
        'object-src' => ["'none'"],
        'base-uri' => ["'self'"],
        'form-action' => ["'self'"],
        'frame-ancestors' => ["'self'"],
        'upgrade-insecure-requests' => true,
    ],

    // CSP directives for development environment
    'development' => [
        'default-src' => ["'self'"],
        'script-src' => [
            "'self'",
            // External script domains
            'https://cdn.jsdelivr.net',
            'https://code.jquery.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            'https://www.youtube.com',
            'https://youtube.com',
            'https://www.youtube-nocookie.com',
            'https://maps.googleapis.com',
            // Development-only directives
            "'unsafe-eval'", // Allow eval() for development tools
            "'unsafe-inline'", // Allow inline scripts for development
            // Nonce for dynamic scripts
            "'nonce-%nonce%'",
        ],
        'script-src-elem' => [
            "'self'",
            // External script domains
            'https://cdn.jsdelivr.net',
            'https://code.jquery.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            'https://www.youtube.com',
            'https://youtube.com',
            'https://www.youtube-nocookie.com',
            'https://maps.googleapis.com',
            // Nonce for dynamic scripts - unsafe-inline is ignored when nonce is present
            "'nonce-%nonce%'",
        ],
        'style-src' => [
            "'self'",
            // External style domains
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://use.fontawesome.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            // Development-only directives
            "'unsafe-inline'", // Allow inline styles for development
            // Nonce for dynamic styles
            "'nonce-%nonce%'",
        ],
        'style-src-elem' => [
            "'self'",
            // External style domains
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://use.fontawesome.com',
            'https://cdn.plyr.io',
            'https://buttons.github.io',
            // Temporary: Allow unsafe-inline for debugging
            "'unsafe-inline'",
            // Nonce for dynamic styles - unsafe-inline is ignored when nonce is present
            "'nonce-%nonce%'",
        ],
        'font-src' => [
            "'self'",
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://use.fontawesome.com',
            'data:',
        ],
        'img-src' => [
            "'self'",
            'data:',
            'https:',
            'blob:',
        ],
        'media-src' => [
            "'self'",
            'data:',
            'blob:',
        ],
        'connect-src' => [
            "'self'",
            'https://api.ipify.org',
            'https://noembed.com',
            'https://cdn.plyr.io',
            'https://api.github.com',
            'https://maps.googleapis.com',
            'ws:', // Allow WebSocket connections for development tools
            'wss://*.livewire.io', // For Livewire WebSocket connections
            'https://*.livewire.io', // For Livewire HTTP connections
        ],
        'frame-src' => [
            "'self'",
            'https://www.youtube.com',
            'https://youtube.com',
            'https://youtu.be',
            'https://www.youtube-nocookie.com',
        ],
        'object-src' => ["'none'"],
        'base-uri' => ["'self'"],
        'form-action' => ["'self'"],
        'frame-ancestors' => ["'self'"],
    ],

    // CSP violation handling
    'reporting' => [
        'enabled' => true,
        'log' => true,
        'email' => env('CSP_REPORT_EMAIL'),
        'threshold' => 10, // Number of similar violations before sending an alert
        'throttle' => 60, // Minutes to wait before sending another alert for the same violation
    ],
];