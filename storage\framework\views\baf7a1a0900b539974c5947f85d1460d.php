<!--
=========================================================
* Corporate UI - v1.0.0
=========================================================

* Product Page: https://www.creative-tim.com/product/corporate-ui
* Copyright 2022 Creative Tim (https://www.creative-tim.com)
* Licensed under MIT (https://www.creative-tim.com/license)
* Coded by Creative Tim

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
-->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="csp-nonce" content="<?php echo e(csp_nonce()); ?>">
    <meta name="dashboard-route" content="<?php echo e(route('dashboard')); ?>">
    <meta name="corporate-ui-script" content="<?php echo e(asset('assets/js/corporate-ui-dashboard.min.js?v=1.0.0')); ?>">

    <!-- Pre-Livewire CSP Patch - Must be first to patch DOM methods -->
    <script src="<?php echo e(asset('assets/js/extracted/pre-livewire-csp-patch.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- Livewire.js Interceptor - Additional protection -->
    <script src="<?php echo e(asset('assets/js/extracted/livewire-js-interceptor.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- Immediate CSP Fix - General protection -->
    <script src="<?php echo e(asset('assets/js/extracted/immediate-csp-fix.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <?php if(config('app.is_demo')): ?>
        <title itemprop="name">
            Corporate UI Dashboard Laravel by Creative Tim & UPDIVISION
        </title>
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:site" content="@CreativeTim" />
        <meta name="twitter:creator" content="@CreativeTim" />
        <meta name="twitter:title" content="Corporate UI Dashboard Laravel by Creative Tim & UPDIVISION" />
        <meta name="twitter:description" content="Fullstack tool for building Laravel apps with hundreds of UI components
            and ready-made CRUDs" />
        <meta name="twitter:image"
            content="https://s3.amazonaws.com/creativetim_bucket/products/737/original/corporate-ui-dashboard-laravel.jpg?1695288974" />
        <meta name="twitter:url" content="https://www.creative-tim.com/live/corporate-ui-dashboard-laravel" />
        <meta name="description" content=""Fullstack tool for building Laravel apps with hundreds of UI components
            and ready-made CRUDs">
        <meta name="keywords"
            content="creative tim, updivision, html dashboard, laravel, api, html css dashboard laravel,  Corporate UI Dashboard Laravel,  Corporate UI Laravel,  Corporate Dashboard Laravel, UI Dashboard Laravel, Laravel admin, laravel dashboard, Laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, frontend, responsive bootstrap 5 dashboard, corporate dashboard laravel,  Corporate UI Dashboard Laravel">
        <meta property="og:app_id" content="655968634437471">
        <meta property="og:type" content="product">
        <meta property="og:title" content="Corporate UI Dashboard Laravel by Creative Tim & UPDIVISION">
        <meta property="og:url" content="https://www.creative-tim.com/live/corporate-ui-dashboard-laravel">
        <meta property="og:image"
            content="https://s3.amazonaws.com/creativetim_bucket/products/737/original/corporate-ui-dashboard-laravel.jpg?1695288974">
        <meta property="product:price:amount" content="FREE">
        <meta property="product:price:currency" content="USD">
        <meta property="product:availability" content="in Stock">
        <meta property="product:brand" content="Creative Tim">
        <meta property="product:category" content="Admin &amp; Dashboards">
        <meta name="data-turbolinks-track" content="false">
    <?php endif; ?>
    <link rel="apple-touch-icon" sizes="76x76" href="../assets/img/apple-icon.png">
    <link rel="icon" type="image/png" href="../assets/img/favicon.png">
    <title>
    Islamic Finance Courses Hub
    </title>
    <!--     Fonts and icons     -->
    <link
        href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700|Noto+Sans:300,400,500,600,700,800|PT+Mono:300,400,500,600,700"
        rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <!-- Nucleo Icons -->
    <link href="<?php echo e(asset('assets/css/nucleo-icons.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/nucleo-svg.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <!-- Font Awesome Icons -->
    <!-- <script src="https://kit.fontawesome.com/349ee9c857.js" crossorigin="anonymous"></script> -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>>

    <link href="<?php echo e(asset('assets/css/nucleo-svg.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <!-- CSS Files -->
    <link id="pagestyle" href="<?php echo e(asset('assets/css/corporate-ui-dashboard.css?v=1.0.0')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/corporate-ui-fixes.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/livewire.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/livewire-dynamic.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/livewire-csp-fix.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/github-buttons.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/buttons-fix.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/custom-scrollbar.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/theme-gradients.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    <link href="<?php echo e(asset('assets/css/extracted/comprehensive-signin-csp-fix.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />

    <!-- Livewire Base Styles - CSP Compliant -->
    <link href="<?php echo e(asset('assets/css/extracted/livewire-base-styles.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />

    <!-- Dynamic Styles CSP Fix -->
    <link href="<?php echo e(asset('assets/css/extracted/dynamic-styles-fix.css')); ?>" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />
    

<!-- In the <head> -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css" rel="stylesheet" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?> />

    <script src="<?php echo e(asset('assets/js/extracted/dashboard-route.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>

</head>

<body class="g-sidenav-show  bg-gray-100">
    <?php
        $topSidenavArray = ['wallet', 'profile'];
        $topSidenavTransparent = ['signin', 'signup', 'sign-in', 'sign-up'];
        $topSidenavRTL = ['RTL'];
    ?>

    <!-- Include Global Navbar only if not on sign-in/sign-up pages -->
    <?php
        try {
            $currentRoute = request()->route();
            $routeName = $currentRoute ? $currentRoute->getName() : null;
        } catch (\Exception $e) {
            $routeName = null;
        }
    ?>
    
    <?php if($routeName && !in_array($routeName, $topSidenavTransparent)): ?>
        <?php if (isset($component)) { $__componentOriginal0f1e1c18620a01537b7924e43a775a2a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0f1e1c18620a01537b7924e43a775a2a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app.navbar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app.navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0f1e1c18620a01537b7924e43a775a2a)): ?>
<?php $attributes = $__attributesOriginal0f1e1c18620a01537b7924e43a775a2a; ?>
<?php unset($__attributesOriginal0f1e1c18620a01537b7924e43a775a2a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0f1e1c18620a01537b7924e43a775a2a)): ?>
<?php $component = $__componentOriginal0f1e1c18620a01537b7924e43a775a2a; ?>
<?php unset($__componentOriginal0f1e1c18620a01537b7924e43a775a2a); ?>
<?php endif; ?>
    <?php endif; ?>

    <?php if($routeName && in_array($routeName, $topSidenavArray)): ?>
        <?php if (isset($component)) { $__componentOriginal3f0e1433f654138e4c0f4be7920c4aee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3f0e1433f654138e4c0f4be7920c4aee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidenav-top','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidenav-top'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3f0e1433f654138e4c0f4be7920c4aee)): ?>
<?php $attributes = $__attributesOriginal3f0e1433f654138e4c0f4be7920c4aee; ?>
<?php unset($__attributesOriginal3f0e1433f654138e4c0f4be7920c4aee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3f0e1433f654138e4c0f4be7920c4aee)): ?>
<?php $component = $__componentOriginal3f0e1433f654138e4c0f4be7920c4aee; ?>
<?php unset($__componentOriginal3f0e1433f654138e4c0f4be7920c4aee); ?>
<?php endif; ?>
    <?php elseif($routeName && in_array($routeName, $topSidenavTransparent)): ?>

    <?php elseif($routeName && in_array($routeName, $topSidenavRTL)): ?>
    <?php else: ?>
        <!-- <?php if (isset($component)) { $__componentOriginal790df3a3003b05a46d3e5fdd59aeab47 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal790df3a3003b05a46d3e5fdd59aeab47 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal790df3a3003b05a46d3e5fdd59aeab47)): ?>
<?php $attributes = $__attributesOriginal790df3a3003b05a46d3e5fdd59aeab47; ?>
<?php unset($__attributesOriginal790df3a3003b05a46d3e5fdd59aeab47); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal790df3a3003b05a46d3e5fdd59aeab47)): ?>
<?php $component = $__componentOriginal790df3a3003b05a46d3e5fdd59aeab47; ?>
<?php unset($__componentOriginal790df3a3003b05a46d3e5fdd59aeab47); ?>
<?php endif; ?> -->
    <?php endif; ?>

    <?php if (! empty(trim($__env->yieldContent('main-content')))): ?>
        <?php echo $__env->yieldContent('main-content'); ?>
    <?php else: ?>
        <?php echo e($slot ?? ''); ?>

    <?php endif; ?>

    <div class="fixed-plugin">
        <a class="fixed-plugin-button text-dark position-fixed px-3 py-2">
            <i class="fa fa-cog py-2"></i>
        </a>
        <div class="card shadow-lg ">
            <div class="card-header pb-0 pt-3 ">
                <div class="float-start">
                    <h5 class="mt-3 mb-0">Corporate UI Configurator</h5>
                    <p>See our dashboard options.</p>
                </div>
                <div class="float-end mt-4">
                    <button class="btn btn-link text-dark p-0 fixed-plugin-close-button">
                        <i class="fa fa-close"></i>
                    </button>
                </div>
                <!-- End Toggle Button -->
            </div>
            <hr class="horizontal dark my-1">
            <div class="card-body pt-sm-3 pt-0">
                <!-- Sidebar Backgrounds -->
                <div>
                    <h6 class="mb-0">Sidebar Colors</h6>
                </div>
                <a href="#" class="switch-trigger background-color">
                    <div class="badge-colors my-2 text-start">
                        <span class="badge filter bg-gradient-primary active" data-color="primary"></span>
                        <span class="badge filter bg-gradient-info" data-color="info"></span>
                        <span class="badge filter bg-gradient-success" data-color="success"></span>
                        <span class="badge filter bg-gradient-warning" data-color="warning"></span>
                        <span class="badge filter bg-gradient-danger" data-color="danger"></span>
                    </div>
                </a>
                <!-- Sidenav Type -->
                <div class="mt-3">
                    <h6 class="mb-0">Sidenav Type</h6>
                    <p class="text-sm">Choose between 2 different sidenav types.</p>
                </div>
                <div class="d-flex">
                    <button class="btn bg-gradient-primary w-100 px-3 mb-2 active" data-class="bg-slate-900">Dark</button>
                    <button class="btn bg-gradient-primary w-100 px-3 mb-2 ms-2" data-class="bg-white">White</button>
                </div>
                <p class="text-sm d-xl-none d-block mt-2">You can change the sidenav type just on desktop view.</p>
                <!-- Navbar Fixed -->
                <div class="mt-3">
                    <h6 class="mb-0">Navbar Fixed</h6>
                </div>
                <div class="form-check form-switch ps-0">
                    <input class="form-check-input mt-1 ms-auto" type="checkbox" id="navbarFixed">
                </div>
                <hr class="horizontal dark my-sm-4">
                <a class="btn bg-gradient-dark w-100" target="_blank"
                    href="https://www.creative-tim.com/product/corporate-ui-dashboard-laravel">Free Download</a>
                <a class="btn btn-outline-dark w-100" target="_blank"
                    href="https://www.creative-tim.com/learning-lab/bootstrap/installation-guide/corporate-ui-dashboard">View
                    documentation</a>
                <div class="w-100 text-center">
                    <a class="github-button" target="_blank" href="https://github.com/creativetimofficial/corporate-ui-dashboard-laravel"
                        data-icon="octicon-star" data-size="large" data-show-count="true"
                        aria-label="Star creativetimofficial/corporate-ui-dashboard on GitHub">Star</a>
                    <h6 class="mt-3">Thank you for sharing!</h6>
                    <a href="https://twitter.com/intent/tweet?text=Check%20Corporate%20UI%20Dashboard%20made%20by%20%40CreativeTim%20%26%20%40UPDIVISION%20%23webdesign%20%23dashboard%20%23bootstrap5%20%23laravel&amp;url=https%3A%2F%2Fwww.creative-tim.com%2Fproduct%2Fcorporate-ui-dashboard-laravel"
                    class="btn btn-dark mb-0 me-2" target="_blank">
                        <i class="fab fa-twitter me-1" aria-hidden="true"></i> Tweet
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u=https://www.creative-tim.com/product/corporate-ui-dashboard-laravel"
                        class="btn btn-dark mb-0 me-2" target="_blank">
                        <i class="fab fa-facebook-square me-1" aria-hidden="true"></i> Share
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!--   Core JS Files   -->
    <script src="<?php echo e(asset('assets/js/core/popper.min.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/core/bootstrap.min.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/plugins/perfect-scrollbar.min.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/plugins/smooth-scrollbar.min.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/plugins/chartjs.min.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/plugins/swiper-bundle.min.js')); ?>" type="text/javascript" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- Github buttons -->
    <script src="<?php echo e(asset('assets/js/extracted/github-buttons.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/extracted/github-buttons-csp-fix.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- App Layout JavaScript -->
    <script src="<?php echo e(asset('assets/js/extracted/app-layout.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/extracted/buttons-fix.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <script src="<?php echo e(asset('assets/js/extracted/livewire-helpers.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/extracted/livewire-csp-fix.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/extracted/livewire-dynamic-style-fix.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <script src="<?php echo e(asset('assets/js/extracted/livewire-style-handler.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- Event Handler CSP Fixer -->
    <script src="<?php echo e(asset('js/event-handler-fixer.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- Dynamic Styles Handler -->
    <script src="<?php echo e(asset('js/dynamic-styles-handler.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
<!-- Before closing </body> -->

</body>

</html>
<?php /**PATH D:\laravel\iec-courses-app\resources\views/layouts/app.blade.php ENDPATH**/ ?>