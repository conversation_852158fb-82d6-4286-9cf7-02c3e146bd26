<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AddSecurityHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $isLocal = in_array(app()->environment(), ['local', 'development']);

        // Proactive HTTPS enforcement (before processing request)
        // Only enforce HTTPS in production environment and skip for auth flows
        $skipHttpsRedirectPaths = ['sign-in', 'signin', 'login', 'logout', 'admin', '/', 'dashboard'];
        $shouldSkipHttpsRedirect = false;
        
        foreach ($skipHttpsRedirectPaths as $path) {
            if ($request->path() === $path || str_contains($request->path(), $path)) {
                $shouldSkipHttpsRedirect = true;
                break;
            }
        }
        
        // Only redirect to HTTPS in production and when not on auth/admin paths
        if (app()->environment('production') && !$request->secure() && !$shouldSkipHttpsRedirect) {
            return redirect()->secure($request->getRequestUri(), 301);
        }

        $response = $next($request);

        // Check for insecure external redirects (skip for local development)
        if ($response->isRedirect() && !$isLocal) {
            $location = $response->headers->get('Location');
            if ($location && $this->isInsecureExternalRedirect($location, $request)) {
                abort(403, 'Insecure external redirect blocked');
            }
        }

        // For local development or insecure connections, we need to be more permissive
        // This will allow microphone access in both secure and insecure contexts
        if ($isLocal) {
            // More permissive for local development - Use only Permissions-Policy (modern standard)
            $response->headers->set('Permissions-Policy', 'microphone=*, camera=*');
        } else {
            // More restrictive for production - Use only Permissions-Policy (modern standard)
            $response->headers->set('Permissions-Policy', 'microphone=self, camera=self');
        }

        // Add comprehensive security headers (replacing .htaccess functionality)
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Frame-Options', 'DENY', true); // Force override any existing header
        
        // Additional security headers
        $response->headers->set('X-Permitted-Cross-Domain-Policies', 'none');
        $response->headers->set('Cross-Origin-Embedder-Policy', 'require-corp');
        $response->headers->set('Cross-Origin-Opener-Policy', 'same-origin');
        $response->headers->set('Cross-Origin-Resource-Policy', 'same-origin');

        // Server signature protection (application level)
        $response->headers->set('Server', 'Apache'); // Hide server details

        // For CORS issues with recording (only in local environment)
        if ($isLocal) {
            $response->headers->set('Access-Control-Allow-Origin', '*');
        }

        // Set Strict-Transport-Security header (HTTPS enforcement)
        if (!$isLocal && $request->isSecure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        // Skip CSP entirely for local development to avoid blocking issues
        if ($isLocal) {
            // Don't apply CSP in local environment
        } else {
            // Only apply CSP to specific pages that need it, skip for redirects and auth flows
            $skipCSPPaths = ['/', 'dashboard', 'admin', 'sign-in', 'signin', 'login', 'logout'];
            $shouldSkipCSP = false;
            
            foreach ($skipCSPPaths as $path) {
                if ($request->path() === trim($path, '/') || $request->path() === $path) {
                    $shouldSkipCSP = true;
                    break;
                }
            }
            
            // Also skip CSP for redirects
            if ($response->isRedirect()) {
                $shouldSkipCSP = true;
            }
            
            if (!$shouldSkipCSP) {
            // Set Content-Security-Policy - Force override any server-level CSP
            $cspHeader = "default-src 'self'; " .
                        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com https://cdn.plyr.io https://buttons.github.io https://www.youtube.com https://youtube.com https://www.youtube-nocookie.com https://maps.googleapis.com; " .
                        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://cdn.plyr.io; " .
                        "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://cdn.plyr.io; " .
                        "font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com data:; " .
                        "img-src 'self' data: https:; " .
                        "media-src 'self' data: blob:; " .
                        "connect-src 'self' https://api.ipify.org https://noembed.com https://cdn.plyr.io https://api.github.com https://maps.googleapis.com; " .
                        "frame-src 'self' https://www.youtube.com https://youtube.com https://youtu.be https://www.youtube-nocookie.com; " .
                        "object-src 'none';";
            
            // Force set CSP header - remove any existing CSP headers first
            $response->headers->remove('Content-Security-Policy');
            $response->headers->remove('X-Content-Security-Policy');
            $response->headers->remove('X-WebKit-CSP');

            // Set the new CSP header
            $response->headers->set('Content-Security-Policy', $cspHeader);
            }
        }

        // Set Referrer-Policy
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Disable Caching for sensitive pages
        $sensitivePaths = ['login', 'register', 'dashboard', 'admin', 'profile'];
        $currentPath = $request->path();

        $isSensitivePath = false;
        foreach ($sensitivePaths as $path) {
            if (str_contains($currentPath, $path)) {
                $isSensitivePath = true;
                break;
            }
        }

        if ($isSensitivePath) {
            $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
        }

        return $response;
    }

    /**
     * Check if redirect is to an insecure external endpoint
     */
    private function isInsecureExternalRedirect(string $location, Request $request): bool
    {
        // Block HTTP redirects to external IPs or domains
        if (str_starts_with($location, 'http://')) {
            $host = parse_url($location, PHP_URL_HOST);
            
            // Block redirects to IP addresses (potential server disclosure)
            if (filter_var($host, FILTER_VALIDATE_IP)) {
                return true;
            }
            
            // Block redirects to non-standard ports (like 2030, 8080, etc.)
            $port = parse_url($location, PHP_URL_PORT);
            if ($port && !in_array($port, [80, 443])) {
                return true;
            }
            
            // Block redirects to external domains
            $currentHost = $request->getHost();
            if ($host !== $currentHost) {
                return true;
            }
        }

        // Block redirects containing management ports in the URL path
        if (preg_match('/:(2030|2031|2032|2083|2087|8080|8443|10000|20000)/', $location)) {
            return true;
        }

        return false;
    }
}
