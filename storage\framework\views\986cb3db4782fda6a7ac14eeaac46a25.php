<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Admin Dashboard - <?php echo $__env->yieldContent('title'); ?></title>

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link href="<?php echo e(asset('assets/css/nucleo-icons.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/css/nucleo-svg.css')); ?>" rel="stylesheet" />
    <link id="pagestyle" href="<?php echo e(asset('assets/css/corporate-ui-dashboard.css?v=1.0.0')); ?>" rel="stylesheet" />

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <style>
        .sidebar {
            min-height: 100vh;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link {
            color: #333;
            padding: 0.8rem 1rem;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #f8f9fa;
            color: #0d6efd;
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            padding: 2rem;
        }

        .card-stats {
            transition: all 0.3s ease;
        }

        .card-stats:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Include Global Navbar -->
    <?php if (isset($component)) { $__componentOriginal0f1e1c18620a01537b7924e43a775a2a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0f1e1c18620a01537b7924e43a775a2a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app.navbar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app.navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0f1e1c18620a01537b7924e43a775a2a)): ?>
<?php $attributes = $__attributesOriginal0f1e1c18620a01537b7924e43a775a2a; ?>
<?php unset($__attributesOriginal0f1e1c18620a01537b7924e43a775a2a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0f1e1c18620a01537b7924e43a775a2a)): ?>
<?php $component = $__componentOriginal0f1e1c18620a01537b7924e43a775a2a; ?>
<?php unset($__componentOriginal0f1e1c18620a01537b7924e43a775a2a); ?>
<?php endif; ?>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="py-4 px-3 mb-4 <?php if(Auth::user()->isSuperAdmin()): ?> bg-danger <?php else: ?> bg-primary <?php endif; ?> text-white">
                        <div class="text-center">
                            <?php if(Auth::user()->isSuperAdmin()): ?>
                                <h4>Super Admin Panel</h4>
                            <?php else: ?>
                                <h4>Admin Panel</h4>
                            <?php endif; ?>
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                                <i class="fas fa-home"></i> Main Dashboard
                            </a>
                        </li>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'courses')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.courses')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.courses') ? 'active' : ''); ?>">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'lectures')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.lectures')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.lectures') ? 'active' : ''); ?>">
                                <i class="fas fa-video"></i> Lectures
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'users')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.users')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.users') ? 'active' : ''); ?>">
                                <i class="fas fa-users"></i> Users
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'questions')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.questions.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.questions.index') ? 'active' : ''); ?>">
                                <i class="fas fa-question-circle"></i> Questions
                                <?php
                                    if (Auth::user()->isSuperAdmin()) {
                                        // Super admin sees all pending questions
                                        $pendingCount = \App\Models\Question::where('status', 'pending')->count();
                                    } else {
                                        // Regular admin only sees pending questions from users assigned to them
                                        $assignedUserIds = \App\Models\AdminUserAssignment::where('admin_id', Auth::id())->pluck('user_id')->toArray();
                                        $pendingCount = \App\Models\Question::whereIn('user_id', $assignedUserIds)
                                                        ->where('status', 'pending')
                                                        ->count();
                                    }
                                ?>
                                <?php if($pendingCount > 0): ?>
                                    <span class="badge bg-danger ml-2"><?php echo e($pendingCount); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'roles')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.roles')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.roles') ? 'active' : ''); ?>">
                                <i class="fas fa-user-tag"></i> Roles
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'coupons')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.coupons.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.coupons.*') ? 'active' : ''); ?>">
                                <i class="fas fa-tags"></i> Coupons
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'orders')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.orders')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.orders') ? 'active' : ''); ?>">
                                <i class="fas fa-receipt"></i> Orders
                                <?php
                                    if (Auth::user()->isSuperAdmin()) {
                                        // Super admin sees all pending orders
                                        $pendingOrderCount = \App\Models\Order::where('status', 'pending')->count();
                                    } else {
                                        // Regular admin only sees pending orders from users assigned to them
                                        $assignedUserIds = \App\Models\AdminUserAssignment::where('admin_id', Auth::id())->pluck('user_id')->toArray();
                                        $pendingOrderCount = \App\Models\Order::whereIn('user_id', $assignedUserIds)
                                                        ->where('status', 'pending')
                                                        ->count();
                                    }
                                ?>
                                <?php if($pendingOrderCount > 0): ?>
                                    <span class="badge bg-warning ml-2"><?php echo e($pendingOrderCount); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'payment_methods')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.payment-methods.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.payment-methods.*') ? 'active' : ''); ?>">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'instructor_profiles')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.instructor-profiles')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.instructor-profiles') ? 'active' : ''); ?>">
                                <i class="fas fa-chalkboard-teacher"></i> Instructor Profiles
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'quizzes')->where('is_allowed', true)->exists()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.quizzes.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.quizzes.*') && !request()->routeIs('admin.quiz-reviews.*') ? 'active' : ''); ?>">
                                <i class="fas fa-clipboard-check"></i> Quizzes
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.quiz-reviews.pending')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.quiz-reviews.*') ? 'active' : ''); ?>">
                                <i class="fas fa-clipboard-list"></i> Quiz Reviews
                                <?php
                                    if (Auth::user()->isSuperAdmin()) {
                                        // Super admin sees all pending quiz reviews
                                        $pendingReviewCount = \App\Models\QuizAnswer::where('review_status', 'pending_review')->count();
                                    } else {
                                        // Regular admin only sees pending reviews from users assigned to them
                                        $assignedUserIds = \App\Models\AdminUserAssignment::where('admin_id', Auth::id())->pluck('user_id')->toArray();
                                        $pendingReviewCount = \App\Models\QuizAnswer::where('review_status', 'pending_review')
                                                            ->whereHas('attempt', function($query) use ($assignedUserIds) {
                                                                $query->whereIn('user_id', $assignedUserIds);
                                                            })
                                                            ->count();
                                    }
                                ?>
                                <?php if($pendingReviewCount > 0): ?>
                                    <span class="badge bg-warning ml-2"><?php echo e($pendingReviewCount); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->isSuperAdmin()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.assignments.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.assignments.*') ? 'active' : ''); ?>">
                                <i class="fas fa-user-cog"></i> Admin-User Assignments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('admin.permissions')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.permissions') ? 'active' : ''); ?>">
                                <i class="fas fa-lock"></i> Admin Permissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('devices.index')); ?>" class="nav-link <?php echo e(request()->routeIs('devices.*') ? 'active' : ''); ?>">
                                <i class="fas fa-mobile-alt"></i> Device Management
                            </a>
                        </li>
                        <?php endif; ?>

                        <li class="nav-item mt-5">
                            <a href="<?php echo e(route('dashboard')); ?>" class="nav-link text-danger">
                                <i class="fas fa-sign-out-alt"></i> Back to Site
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo $__env->yieldContent('header', 'Dashboard'); ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php echo $__env->yieldContent('actions'); ?>
                    </div>
                </div>

                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="main-content">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <!-- Event Handler CSP Fixer -->
    <script src="<?php echo e(asset('js/event-handler-fixer.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>
    <!-- Dynamic Styles Handler -->
    <script src="<?php echo e(asset('js/dynamic-styles-handler.js')); ?>" <?php echo e(app('csp.nonce')->getNonceAttribute()); ?>></script>

    <?php echo $__env->yieldContent('scripts'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\laravel\iec-courses-app\resources\views/admin/layout.blade.php ENDPATH**/ ?>